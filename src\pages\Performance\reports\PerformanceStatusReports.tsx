import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { 
  FileText, 
  Download, 
  Filter, 
  Search,
  Target,
  AlertCircle,
  Loader2,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import * as XLSX from 'xlsx';

// Types
interface PerformanceStatusResult {
  employee_no: string;
  staff_name: string | null;
  Designation: string | null;
  department: string | null;
  Team: string | null;
  is_active: boolean;
  Team_leader: string | null;
  department_head: string | null;
  appraisal_status: string | null;
}

interface Department {
  id: number;
  name: string;
  title?: string;
  dep_head?: string;
  dep_head_assistant?: string;
}

interface Employee {
  id: number;
  employee_no_id: string;
  first_name: string;
  last_name: string;
  email: string;
  job_title: string;
  department_id: number;
  is_active: number;
}

interface Filters {
  category: string;
  department: string;
  employee_id: string;
}

const PerformanceStatusReports: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  // State management
  const [loading, setLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [data, setData] = useState<PerformanceStatusResult[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  
  const [filters, setFilters] = useState<Filters>({
    category: 'all',
    department: 'all',
    employee_id: 'all'
  });

  // Employee search state
  const [employeeSearch, setEmployeeSearch] = useState('');

  // Fetch departments
  const fetchDepartments = async () => {
    try {
      const response = await fetch(`${BASE_URL}/users/departments`, {
        headers: { 'Authorization': `Token ${token}` }
      });
      if (!response.ok) throw new Error('Failed to fetch departments');
      const data = await response.json();
      setDepartments(data);
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast({
        title: "Error",
        description: "Failed to fetch departments",
        variant: "destructive",
      });
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    try {
      const response = await fetch(`${BASE_URL}/employee_details/?team=ALL&department=ALL`, {
        headers: { 'Authorization': `Token ${token}` }
      });
      if (!response.ok) throw new Error('Failed to fetch employees');
      const data = await response.json();
      setEmployees(data.results || []);
    } catch (error) {
      console.error('Error fetching employees:', error);
      toast({
        title: "Error",
        description: "Failed to fetch employees",
        variant: "destructive",
      });
    }
  };

  // Filter employees by department
  useEffect(() => {
    if (filters.department && filters.department !== 'all') {
      const filtered = employees.filter(emp => emp.department_id && emp.department_id.toString() === filters.department);
      setFilteredEmployees(filtered);
    } else {
      setFilteredEmployees(employees);
    }
  }, [filters.department, employees]);

  // Fetch performance status
  const fetchPerformanceStatus = async () => {
    // Check if at least one filter is applied
    if (filters.category === 'all' && filters.department === 'all' && filters.employee_id === 'all') {
      toast({
        title: "Filter Required",
        description: "Please select at least one filter (Category, Department, or Employee) to generate reports",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const params = new URLSearchParams();
      
      if (filters.category && filters.category !== 'all') {
        params.append('Category', filters.category);
      }
      
      if (filters.department && filters.department !== 'all') {
        params.append('Department', filters.department);
      }
      
      if (filters.employee_id && filters.employee_id !== 'all') {
        params.append('employee_id', filters.employee_id);
      }

      const response = await fetch(`${BASE_URL}/performance-status-check/?${params.toString()}`, {
        headers: { 'Authorization': `Token ${token}` }
      });

      if (!response.ok) throw new Error('Failed to fetch performance status');
      const responseData = await response.json();
      setData(responseData.results || []);
      
      toast({
        title: "Success",
        description: `Found ${responseData.results?.length || 0} employee records`,
      });
    } catch (error) {
      console.error('Error fetching performance status:', error);
      toast({
        title: "Error",
        description: "Failed to fetch performance status",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string | null) => {
    if (!status) {
      return <Badge variant="secondary" className="bg-gray-100 text-gray-600">Not Started</Badge>;
    }
    
    switch (status.toLowerCase()) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-700">Completed</Badge>;
      case 'in review':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-700">In Review</Badge>;
      case 'pending':
        return <Badge variant="default" className="bg-orange-100 text-orange-700">Pending</Badge>;
      case 'draft':
        return <Badge variant="default" className="bg-blue-100 text-blue-700">Draft</Badge>;
      default:
        return <Badge variant="secondary" className="bg-gray-100 text-gray-600">{status}</Badge>;
    }
  };

  // Get status icon
  const getStatusIcon = (status: string | null) => {
    if (!status) return <XCircle className="h-4 w-4 text-gray-400" />;
    
    switch (status.toLowerCase()) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in review':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      default:
        return <Clock className="h-4 w-4 text-blue-600" />;
    }
  };

  // Export to Excel
  const exportToExcel = async () => {
    if (data.length === 0) {
      toast({
        title: "No Data",
        description: "No data available to export",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    try {
      const workbook = XLSX.utils.book_new();
      const sheetData: any[][] = [];

      // Company Header
      sheetData.push(['OPTIVEN LIMITED']);
      sheetData.push(['PERFORMANCE STATUS REPORT']);
      sheetData.push([`Generated on: ${new Date().toLocaleDateString()}`]);
      sheetData.push([]); // Empty row

      // Summary Section
      sheetData.push(['REPORT SUMMARY']);
      sheetData.push(['Total Employees', data.length]);
      if (filters.category && filters.category !== 'all') {
        sheetData.push(['Report Category', filters.category]);
      }
      if (filters.department && filters.department !== 'all') {
        const deptName = departments.find(d => d.id.toString() === filters.department)?.name;
        sheetData.push(['Department Filter', deptName || 'Unknown']);
      }
      if (filters.employee_id && filters.employee_id !== 'all') {
        const empName = employees.find(e => e.employee_no_id === filters.employee_id);
        sheetData.push(['Employee Filter', `${empName?.first_name} ${empName?.last_name} (${filters.employee_id})` || filters.employee_id]);
      }
      sheetData.push(['Generated By', 'HRMS System']);
      sheetData.push([]); // Empty row

      // Headers
      sheetData.push([
        'Employee No.',
        'Staff Name',
        'Department',
        'Designation',
        'Team',
        'Team Leader',
        'Department Head',
        'Appraisal Status',
        'Active Status'
      ]);

      // Data rows
      data.forEach((employee) => {
        const teamLeader = employee.Team_leader || employee.department_head || 'N/A';

        sheetData.push([
          employee.employee_no || 'N/A',
          employee.staff_name || 'N/A',
          employee.department || 'N/A',
          employee.Designation || 'N/A',
          employee.Team || 'N/A',
          teamLeader,
          employee.department_head || 'N/A',
          employee.appraisal_status || 'Not Started',
          employee.is_active ? 'Active' : 'Inactive'
        ]);
      });

      const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

      // Set column widths
      worksheet['!cols'] = [
        { wch: 15 }, // Employee No.
        { wch: 25 }, // Staff Name
        { wch: 20 }, // Department
        { wch: 30 }, // Designation
        { wch: 15 }, // Team
        { wch: 20 }, // Team Leader
        { wch: 20 }, // Department Head
        { wch: 15 }, // Appraisal Status
        { wch: 12 }  // Active Status
      ];

      // Apply styling to header row
      const headerRowIndex = 8; // 0-based index of the header row
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: col });
        if (!worksheet[cellAddress]) worksheet[cellAddress] = { t: 's', v: '' };
        worksheet[cellAddress].s = {
          fill: { fgColor: { rgb: '92D050' } },
          font: { bold: true, color: { rgb: 'FFFFFF' } },
          border: {
            top: { style: 'thin', color: { rgb: '000000' } },
            bottom: { style: 'thin', color: { rgb: '000000' } },
            left: { style: 'thin', color: { rgb: '000000' } },
            right: { style: 'thin', color: { rgb: '000000' } }
          }
        };
      }

      const sheetName = 'Performance Status Report';
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

      const fileName = `Performance_Status_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast({
        title: "Export Successful",
        description: `Performance status report exported as ${fileName}`,
      });
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export performance status report",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Initialize data
  useEffect(() => {
    fetchDepartments();
    fetchEmployees();
  }, []);

  return (
    <Screen>
      <div className="space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/performance-dashboard">Performance</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Performance Status Reports</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Performance Status Reports</h1>
            <p className="text-muted-foreground">
              Track appraisal status and progress for all employees
            </p>
          </div>
        </div>

        {/* Filters Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Report Filters
            </CardTitle>
            <CardDescription>
              Select filters to generate performance status reports. At least one filter is required.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              {/* Category Filter */}
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={filters.category}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="Pillar">Pillar</SelectItem>
                    <SelectItem value="Conversion">Conversion</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Department Filter */}
              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Select
                  value={filters.department}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, department: value, employee_id: 'all' }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id.toString()}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Employee Filter with integrated search */}
              <div className="space-y-2">
                <Label htmlFor="employee">Employee</Label>
                <Select
                  value={filters.employee_id}
                  onValueChange={(value) => {
                    setFilters(prev => ({ ...prev, employee_id: value }));
                    setEmployeeSearch('');
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Search and select employee..." />
                  </SelectTrigger>
                  <SelectContent>
                    <div className="p-2 sticky top-0 bg-white z-10">
                      <Input
                        placeholder="Search by name or employee ID..."
                        value={employeeSearch}
                        onChange={(e) => {
                          e.stopPropagation();
                          setEmployeeSearch(e.target.value);
                        }}
                        onKeyDown={(e) => e.stopPropagation()}
                        className="mb-2"
                      />
                    </div>
                    <SelectItem value="all">All Employees</SelectItem>
                    {(filters.department && filters.department !== 'all' ? filteredEmployees : employees)
                      .filter(emp =>
                        !employeeSearch ||
                        `${emp.first_name} ${emp.last_name}`.toLowerCase().includes(employeeSearch.toLowerCase()) ||
                        emp.employee_no_id.toLowerCase().includes(employeeSearch.toLowerCase())
                      )
                      .slice(0, 50)
                      .map((emp) => (
                        <SelectItem key={emp.id} value={emp.employee_no_id}>
                          {emp.first_name} {emp.last_name} ({emp.employee_no_id})
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Generate Button */}
              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button
                  onClick={fetchPerformanceStatus}
                  disabled={loading}
                  className="w-full flex items-center gap-2"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                  Generate Report
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results Section */}
        {loading ? (
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                <span className="ml-2 text-lg">Loading performance status...</span>
              </div>
            </CardContent>
          </Card>
        ) : data.length > 0 ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-green-600" />
                Performance Status Results ({data.length} employees)
              </CardTitle>
              <CardDescription>
                Performance appraisal status for employees
                {filters.category && filters.category !== 'all' && ` in ${filters.category} category`}
                {filters.department && filters.department !== 'all' && ` in ${departments.find(d => d.id.toString() === filters.department)?.name} department`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex justify-end">
                <Button
                  onClick={exportToExcel}
                  disabled={isExporting}
                  className="flex items-center gap-2"
                >
                  {isExporting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4" />
                  )}
                  Export to Excel
                </Button>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee No.</TableHead>
                      <TableHead>Staff Name</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Designation</TableHead>
                      <TableHead>Team</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Active</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.map((employee, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{employee.employee_no}</TableCell>
                        <TableCell>{employee.staff_name || 'N/A'}</TableCell>
                        <TableCell>{employee.department || 'N/A'}</TableCell>
                        <TableCell>{employee.Designation || 'N/A'}</TableCell>
                        <TableCell>{employee.Team || 'N/A'}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(employee.appraisal_status)}
                            {getStatusBadge(employee.appraisal_status)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={employee.is_active ? "default" : "secondary"}
                                 className={employee.is_active ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"}>
                            {employee.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        ) : (filters.category && filters.category !== 'all') || (filters.department && filters.department !== 'all') || (filters.employee_id && filters.employee_id !== 'all') ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Results Found</h3>
              <p className="text-muted-foreground text-center">
                No employee records found for the selected filters. Try adjusting your search criteria.
              </p>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Target className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Select Filters to Begin</h3>
              <p className="text-muted-foreground text-center">
                Choose at least one filter (Category, Department, or Employee) to generate performance status reports.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </Screen>
  );
};

export default PerformanceStatusReports;

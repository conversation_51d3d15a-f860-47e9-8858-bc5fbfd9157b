import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { useDispatch,useSelector } from "react-redux";
import { useEffect } from "react";

import Login from "./pages/Auth/Login";
import Register from "./pages/Auth/Register";
import Dashboard from "./pages/dashboard/Dashboard";
import EmployeeCreate from "./pages/Employee Management/EmployeeCreation";
import JobRequisitionForm from "./pages/Recruitment/JobRequisition";
import RecruitmentPage from "./pages/Recruitment/RequisitionPipeline";
import LeaveManagementPage from "./pages/Leave/LeaveManagement/index_optimized";
import LeaveReports from "./pages/Leave/LeaveReports";
import CalendarEventsPage from "./pages/Calender/CalenderEventsPage";
import JobManagement from "./pages/Performance/JobManagement";
import KpiManagementPage from "./pages/Performance/KpiManagementPage";
import { JobDetailsWrapper } from "./app-components/Performance/jobs/JobList";
import { HRAppraisalSection } from "./pages/Performance/HRAppraisalSection";

import { AppDispatch } from "./redux/store";
import { loadUserFromLocalStorage } from "./redux/features/auth/authSlice";
import ProtectedRoute from "./routes/ProtectedRoute";
import RecruiterHub from "./pages/Recruitment/RecruitmentDashboard";
import { AppraisalPeriods } from "./pages/Performance/AppraisalPeriods";
import { HRDashboardPage } from "./pages/Performance/HRDashboardPage";
import { PerformanceAnalyticsPage } from "./pages/Performance/PerformanceAnalyticsPage";
import ApproveTraining from "./pages/Training/ApproveTraining";
import CreateTrainingNeed from "./pages/Training/CreateTrainingNeed";
import EmployeeAssessmentPage from "./pages/Training/EmployeeAssessment";
import SourcingPage from "./pages/Training/ResourceSourcing";
import ResultsPage from "./pages/Training/TrainingResults";
import EmployeeAnalyticsDashboard from "./pages/Employee Management/EmployeeAnalytics";
import LeaveAnalyticsDashboard from "./pages/Leave/LeaveAnalytics";
import RecruitmentAnalyticsDashboard from "./pages/Recruitment/RecruitmentAnalytics";
import HRKpiApprovalPage from "./pages/Performance/HRKpiApprovalPage";
// import HRKpiApprovalSection from "./app-components/Performance/HRDashboard/HRKpiApprovalSection";
import HRApprovalPage from "./pages/Leave/HRApproval";
import { Toaster } from "react-hot-toast";
import CreateEmployee from "./pages/Employee Management/CreateEmployee";
import EmployeesList from "./pages/Employee Management/EmployeesList";
import Departments from "./pages/Departments & Groups/Departments";
import SetupWizard from "./pages/Auth/SetupWizard";
import Groups from "./pages/Departments & Groups/Group";
import PermissionsManagement from "./pages/settings/PermissionsManagement";
import EmployeePermissionsManagement from "./pages/settings/EmployeePermissions";
import CalendarAnalyticsDashboard from "./pages/Calender/CalendarAnalytics";
import EmployeeReports from "./pages/Employee Management/reports/EmployeeReports";
import DepartmentReports from "./pages/Employee Management/reports/DepartmentReports";
import ContractReports from "./pages/Employee Management/reports/ContractReports";
import ProbationReports from "./pages/Employee Management/reports/ProbationReports";
import PerformanceAppraisalReports from "./pages/Performance/reports/PerformanceAppraisalReports";
import PerformanceAllResultsReports from "./pages/Performance/reports/PerformanceAllResultsReports";
import LeaveClawbackPage from "./pages/Leave/LeaveClawback";
import ForgotPassword from "./pages/Auth/ForgotPassword";
import ResetPassword from "./pages/Auth/ResetPassword";

import ForcePasswordReset from "./pages/Auth/ForcePasswordReset";
import { RootState } from "./redux/store";
import Announcements from "./pages/announcements/Announcements";
import OrganizationChart from "./pages/organizational-chart";

function App() {
  const dispatch = useDispatch<AppDispatch>();
  const { passwordChangeRequired } = useSelector((state: RootState) => state.auth);
  // Load user from local storage when app initializes
  useEffect(() => {
    dispatch(loadUserFromLocalStorage());
  }, [dispatch]);

  return (
    <>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="https://staff.optiven.co.ke/reset-password/:token" element={<ResetPassword/>} />
          <Route path="/force-password-reset" element={<ForcePasswordReset />} />
          <Route path="/register" element={<Register />} />
          <Route path="/setup-wizard" element={<SetupWizard />} />

          {/* Check if password change is required and redirect if needed */}
          {passwordChangeRequired ? (
            <>
              {/* Redirect all protected routes to force password reset page when password change is required */}
              <Route path="/*" element={<Navigate to="/force-password-reset" replace />} />
            </>
          ) : (
            <>
             
          {/* Protected Routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/job-management"
            element={
              <ProtectedRoute>
                <JobManagement />
              </ProtectedRoute>
            }
          />
          <Route
            path="/announcements"
            element={
              <ProtectedRoute>
                <Announcements />
              </ProtectedRoute>
            }
          />
          <Route
            path="/kpi-management"
            element={
              <ProtectedRoute>
                <KpiManagementPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/job-management/:jobId"
            element={
              <ProtectedRoute>
                <JobDetailsWrapper />
              </ProtectedRoute>
            }
          />
          <Route
            path="/hr-appraisal"
            element={
              <ProtectedRoute>
                <HRAppraisalSection />
              </ProtectedRoute>
            }
          />


              <Route
                path="/performance-dashboard"
                element={
                  <ProtectedRoute>
                    <HRDashboardPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/performance-analytics"
                element={
                  <ProtectedRoute>
                    <PerformanceAnalyticsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/appraisal-periods"
                element={
                  <ProtectedRoute>
                    <AppraisalPeriods />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/organizational-chart"
                element={
                  <ProtectedRoute>
                    <OrganizationChart />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/groups"
                element={
                  <ProtectedRoute>
                    <Groups />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/permissions"
                element={
                  <ProtectedRoute>
                    <PermissionsManagement />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/emp-permissions"
                element={
                  <ProtectedRoute>
                    <EmployeePermissionsManagement />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/custom-kpi-approval"
                element={
                  <ProtectedRoute>
                    <HRKpiApprovalPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/departments"
                element={
                  <ProtectedRoute>
                    <Departments />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/employee-creation"
                element={
                  <ProtectedRoute>
                    <CreateEmployee />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/create-employee"
                element={
                  <ProtectedRoute>
                    <EmployeeCreate />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/calendar-analytics"
                element={
                  <ProtectedRoute>
                    <CalendarAnalyticsDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/employee-reports"
                element={
                  <ProtectedRoute>
                    <EmployeeReports />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/department-reports"
                element={
                  <ProtectedRoute>
                    <DepartmentReports />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/contract-reports"
                element={
                  <ProtectedRoute>
                    <ContractReports />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/probation-reports"
                element={
                  <ProtectedRoute>
                    <ProbationReports />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/performance-appraisal-reports"
                element={
                  <ProtectedRoute>
                    <PerformanceAppraisalReports />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/performance-all-results-reports"
                element={
                  <ProtectedRoute>
                    <PerformanceAllResultsReports />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/employees-list"
                element={
                  <ProtectedRoute>
                    <EmployeesList />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/create-vacancy"
                element={
                  <ProtectedRoute>
                    <JobRequisitionForm />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/requisition-pipeline"
                element={
                  <ProtectedRoute>
                    <RecruitmentPage />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/leave-management"
                element={
                  <ProtectedRoute>
                    <LeaveManagementPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/leave-clawback"
                element={
                  <ProtectedRoute>
                    <LeaveClawbackPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/leave-reports"
                element={
                  <ProtectedRoute>
                    <LeaveReports />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/calender-management"
                element={
                  <ProtectedRoute>
                    <CalendarEventsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/recruitment-dash"
                element={
                  <ProtectedRoute>
                    <RecruiterHub />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/approve-training"
                element={
                  <ProtectedRoute>
                    <ApproveTraining />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/create-training-need"
                element={
                  <ProtectedRoute>
                    <CreateTrainingNeed />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/training-evaluation"
                element={
                  <ProtectedRoute>
                    <EmployeeAssessmentPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/resource-sourcing"
                element={
                  <ProtectedRoute>
                    <SourcingPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/results"
                element={
                  <ProtectedRoute>
                    <ResultsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/employee-analytics"
                element={
                  <ProtectedRoute>
                    <EmployeeAnalyticsDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/leave-analytics"
                element={
                  <ProtectedRoute>
                    <LeaveAnalyticsDashboard />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/hr-approval"
                element={
                  <ProtectedRoute>
                    <HRApprovalPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/recruitment-analytics"
                element={
                  <ProtectedRoute>
                    <RecruitmentAnalyticsDashboard />
                  </ProtectedRoute>
                }
              />
            </>
          )}
        </Routes>
      </Router>

      <Toaster />
    </>
  );
}

export default App;
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { 
  FileText, 
  Download, 
  Filter, 
  RefreshCw, 
  Search,
  Users,
  Building2,
  Target,
  AlertCircle,
  Loader2
} from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  B<PERSON>c<PERSON><PERSON><PERSON>ist,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import * as XLSX from 'xlsx';

// Types
interface PerformanceResult {
  employee_no: string;
  staff_name: string;
  Designation: string;
  department: string;
  Team: string | null;
  Team_leader: string | null;
  department_head: string;
  what_results: WhatResult[];
}

interface WhatResult {
  id: number;
  what: {
    what: string;
    MIB_target: number;
    Sales_target: number;
    total_marks: number;
    status: string;
    hows: { how: string }[];
  };
  MIB_target: number | null;
  MIB_Achieved: number | null;
  Sales_target: number | null;
  Sales_Achieved: number | null;
  employee_rating: number;
  employee_comments: string;
  supervisor_comments: string;
  supervisor_rating: number;
  manager_comments: string | null;
  manager_rating: number | null;
  extra_comments: string | null;
  extra_rating: number | null;
  created_at: string;
}

interface Department {
  id: number;
  name: string;
  title?: string;
  dep_head?: string;
  dep_head_assistant?: string;
}

interface Employee {
  id: number;
  employee_no_id: string;
  first_name: string;
  last_name: string;
  email: string;
  job_title: string;
  department_id: number;
  is_active: number;
}

interface Filters {
  category: string;
  department: string;
  employee_id: string;
}

const PerformanceAllResultsReports: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  // State management
  const [loading, setLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [data, setData] = useState<PerformanceResult[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  
  const [filters, setFilters] = useState<Filters>({
    category: '',
    department: '',
    employee_id: ''
  });

  // Fetch departments
  const fetchDepartments = async () => {
    try {
      const response = await fetch(`${BASE_URL}/users/departments`, {
        headers: { 'Authorization': `Token ${token}` }
      });
      if (!response.ok) throw new Error('Failed to fetch departments');
      const data = await response.json();
      setDepartments(data);
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast({
        title: "Error",
        description: "Failed to fetch departments",
        variant: "destructive",
      });
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    try {
      const response = await fetch(`${BASE_URL}/employee_details/?team=ALL&department=ALL`, {
        headers: { 'Authorization': `Token ${token}` }
      });
      if (!response.ok) throw new Error('Failed to fetch employees');
      const data = await response.json();
      setEmployees(data.results || []);
    } catch (error) {
      console.error('Error fetching employees:', error);
      toast({
        title: "Error",
        description: "Failed to fetch employees",
        variant: "destructive",
      });
    }
  };

  // Filter employees by department
  useEffect(() => {
    if (filters.department) {
      const filtered = employees.filter(emp => emp.department_id.toString() === filters.department);
      setFilteredEmployees(filtered);
    } else {
      setFilteredEmployees(employees);
    }
  }, [filters.department, employees]);

  // Fetch performance results
  const fetchPerformanceResults = async () => {
    if (!filters.category) {
      toast({
        title: "Category Required",
        description: "Please select a category (Pillar or Conversion) to generate reports",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const params = new URLSearchParams();
      params.append('Category', filters.category);
      
      if (filters.department) {
        params.append('Department', filters.department);
      }
      
      if (filters.employee_id) {
        params.append('employee_id', filters.employee_id);
      }

      const response = await fetch(`${BASE_URL}/performance-all-results/?${params.toString()}`, {
        headers: { 'Authorization': `Token ${token}` }
      });

      if (!response.ok) throw new Error('Failed to fetch performance results');
      const responseData = await response.json();
      setData(responseData.results || []);
      
      toast({
        title: "Success",
        description: `Found ${responseData.results?.length || 0} performance records`,
      });
    } catch (error) {
      console.error('Error fetching performance results:', error);
      toast({
        title: "Error",
        description: "Failed to fetch performance results",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Export to Excel
  const exportToExcel = async () => {
    if (data.length === 0) {
      toast({
        title: "No Data",
        description: "No data available to export",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    try {
      const workbook = XLSX.utils.book_new();
      const sheetData: any[][] = [];

      // Company Header Section (matching the template)
      sheetData.push(['OPTIVEN LIMITED']);
      sheetData.push(['Absa Tower 2nd floor, Loita St']);
      sheetData.push(['P.O BOX: 6525-00600 Nairobi']);
      sheetData.push(['E-Mail: <EMAIL>']);
      sheetData.push(['Mobile: 0790600600 / 0711601019']);
      sheetData.push([]); // Empty row

      // Report Title
      sheetData.push([`${filters.category.toUpperCase()} PERFORMANCE REPORT`]);
      sheetData.push([`Generated on: ${new Date().toLocaleDateString()}`]);
      sheetData.push([]); // Empty row

      // Add headers matching the template
      sheetData.push([
        'NO.',
        'Department',
        'Staff No.',
        'Staff Name',
        'Designation',
        'Email Address',
        'Staff Rating',
        'Staff Comments',
        'TL Rating',
        'TL Comments'
      ]);

      // Transform data to match template
      let rowNumber = 1;
      data.forEach((employee) => {
        // Find employee email from employees list
        const employeeDetails = employees.find(emp => emp.employee_no_id === employee.employee_no);
        const emailAddress = employeeDetails?.email || '';

        employee.what_results.forEach((result) => {
          sheetData.push([
            rowNumber++,
            employee.department,
            employee.employee_no,
            employee.staff_name,
            employee.Designation,
            emailAddress,
            result.employee_rating || '',
            result.employee_comments || '',
            result.supervisor_rating || '',
            result.supervisor_comments || ''
          ]);
        });
      });

      // Add summary section
      sheetData.push([]); // Empty row
      sheetData.push(['SUMMARY']);
      sheetData.push(['Total Employees', data.length]);
      sheetData.push(['Total KPI Records', data.reduce((sum, emp) => sum + emp.what_results.length, 0)]);
      sheetData.push(['Report Category', filters.category]);
      if (filters.department) {
        const deptName = departments.find(d => d.id.toString() === filters.department)?.name;
        sheetData.push(['Department Filter', deptName || 'Unknown']);
      }
      sheetData.push(['Generated By', 'HRMS System']);

      const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

      // Set column widths
      worksheet['!cols'] = [
        { wch: 5 },   // NO.
        { wch: 20 },  // Department
        { wch: 15 },  // Staff No.
        { wch: 25 },  // Staff Name
        { wch: 20 },  // Designation
        { wch: 25 },  // Email Address
        { wch: 12 },  // Staff Rating
        { wch: 50 },  // Staff Comments
        { wch: 12 },  // TL Rating
        { wch: 50 }   // TL Comments
      ];

      // Apply styling to header rows
      const headerRowIndex = 9; // 0-based index of the header row
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

      // Style the header row
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: col });
        if (!worksheet[cellAddress]) continue;

        worksheet[cellAddress].s = {
          font: { bold: true },
          fill: { fgColor: { rgb: "CCCCCC" } },
          alignment: { horizontal: "center" }
        };
      }

      XLSX.utils.book_append_sheet(workbook, worksheet, `${filters.category} Report`);

      const fileName = `${filters.category}_Performance_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast({
        title: "Export Successful",
        description: `Report exported as ${fileName}`,
      });
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export data to Excel",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Initialize data
  useEffect(() => {
    fetchDepartments();
    fetchEmployees();
  }, []);

  return (
    <Screen>
      <div className="space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/performance-dashboard">Performance</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Performance All Results Reports</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Performance All Results Reports</h1>
            <p className="text-muted-foreground">
              Generate comprehensive performance reports for Pillars and Conversion categories
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={exportToExcel}
              disabled={isExporting || data.length === 0}
              className="flex items-center gap-2"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              Export to Excel
            </Button>
          </div>
        </div>

        {/* Filters Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Report Filters
            </CardTitle>
            <CardDescription>
              Select filters to generate performance reports. Category is required.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              {/* Category Filter */}
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={filters.category}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Pillar">Pillar</SelectItem>
                    <SelectItem value="Conversion">Conversion</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Department Filter */}
              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Select
                  value={filters.department}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, department: value, employee_id: '' }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id.toString()}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Employee Filter */}
              <div className="space-y-2">
                <Label htmlFor="employee">Employee</Label>
                <Select
                  value={filters.employee_id}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, employee_id: value }))}
                  disabled={!filters.department}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All employees" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Employees</SelectItem>
                    {filteredEmployees.map((emp) => (
                      <SelectItem key={emp.id} value={emp.employee_no_id}>
                        {emp.first_name} {emp.last_name} ({emp.employee_no_id})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Generate Button */}
              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button
                  onClick={fetchPerformanceResults}
                  disabled={loading || !filters.category}
                  className="w-full flex items-center gap-2"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                  Generate Report
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results Section */}
        {loading ? (
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
              </div>
            </CardContent>
          </Card>
        ) : data.length > 0 ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Performance Results ({data.length} employees)
              </CardTitle>
              <CardDescription>
                Performance data for {filters.category} category
                {filters.department && ` - ${departments.find(d => d.id.toString() === filters.department)?.name}`}
                {filters.employee_id && ` - ${filteredEmployees.find(e => e.employee_no_id === filters.employee_id)?.first_name} ${filteredEmployees.find(e => e.employee_no_id === filters.employee_id)?.last_name}`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Staff No.</TableHead>
                      <TableHead>Staff Name</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Designation</TableHead>
                      <TableHead>KPIs Count</TableHead>
                      <TableHead>Avg Staff Rating</TableHead>
                      <TableHead>Avg TL Rating</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.map((employee, index) => {
                      const avgStaffRating = employee.what_results.reduce((sum, result) => sum + result.employee_rating, 0) / employee.what_results.length;
                      const avgTLRating = employee.what_results.reduce((sum, result) => sum + result.supervisor_rating, 0) / employee.what_results.length;

                      return (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{employee.employee_no}</TableCell>
                          <TableCell>{employee.staff_name}</TableCell>
                          <TableCell>{employee.department}</TableCell>
                          <TableCell>{employee.Designation}</TableCell>
                          <TableCell>{employee.what_results.length}</TableCell>
                          <TableCell>{avgStaffRating.toFixed(1)}</TableCell>
                          <TableCell>{avgTLRating.toFixed(1)}</TableCell>
                          <TableCell>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                // Could implement individual employee export here
                                toast({
                                  title: "Feature Coming Soon",
                                  description: "Individual employee export will be available soon",
                                });
                              }}
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        ) : filters.category ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Results Found</h3>
              <p className="text-muted-foreground text-center">
                No performance data found for the selected filters. Try adjusting your search criteria.
              </p>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Target className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Select Category to Begin</h3>
              <p className="text-muted-foreground text-center">
                Choose a category (Pillar or Conversion) and apply filters to generate performance reports.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </Screen>
  );
};

export default PerformanceAllResultsReports;

import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import {
  FileText,
  Download,
  Filter,
  Search,
  Target,
  AlertCircle,
  Loader2
} from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import * as XLSX from 'xlsx';

// Types
interface PerformanceResult {
  employee_no: string;
  staff_name: string;
  Designation: string;
  department: string;
  Team: string | null;
  Team_leader: string | null;
  department_head: string;
  what_results: WhatResult[];
}

interface WhatResult {
  id: number;
  what: {
    what: string;
    MIB_target: number;
    Sales_target: number;
    total_marks: number;
    status: string;
    hows: { how: string }[];
  };
  MIB_target: number | null;
  MIB_Achieved: number | null;
  Sales_target: number | null;
  Sales_Achieved: number | null;
  employee_rating: number;
  employee_comments: string;
  supervisor_comments: string;
  supervisor_rating: number;
  manager_comments: string | null;
  manager_rating: number | null;
  extra_comments: string | null;
  extra_rating: number | null;
  created_at: string;
}

interface Department {
  id: number;
  name: string;
  title?: string;
  dep_head?: string;
  dep_head_assistant?: string;
}

interface Employee {
  id: number;
  employee_no_id: string;
  first_name: string;
  last_name: string;
  email: string;
  job_title: string;
  department_id: number;
  is_active: number;
}

interface Filters {
  category: string;
  department: string;
  team: string;
  employee_id: string;
}

const PerformanceAllResultsReports: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  // State management
  const [loading, setLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [data, setData] = useState<PerformanceResult[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [teams, setTeams] = useState<any[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);

  const [filters, setFilters] = useState<Filters>({
    category: 'all',
    department: 'all',
    team: 'all',
    employee_id: 'all'
  });

  // Employee search state
  const [employeeSearch, setEmployeeSearch] = useState('');

  // Fetch departments
  const fetchDepartments = async () => {
    try {
      const response = await fetch(`${BASE_URL}/users/departments`, {
        headers: { 'Authorization': `Token ${token}` }
      });
      if (!response.ok) throw new Error('Failed to fetch departments');
      const data = await response.json();
      setDepartments(data);
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast({
        title: "Error",
        description: "Failed to fetch departments",
        variant: "destructive",
      });
    }
  };

  // Fetch teams
  const fetchTeams = async () => {
    try {
      const response = await fetch(`${BASE_URL}/users/organization_groups`, {
        headers: { 'Authorization': `Token ${token}` }
      });
      if (!response.ok) throw new Error('Failed to fetch teams');
      const data = await response.json();
      setTeams(data);
    } catch (error) {
      console.error('Error fetching teams:', error);
      toast({
        title: "Error",
        description: "Failed to fetch teams",
        variant: "destructive",
      });
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    try {
      const response = await fetch(`${BASE_URL}/employee_details/?team=ALL&department=ALL`, {
        headers: { 'Authorization': `Token ${token}` }
      });
      if (!response.ok) throw new Error('Failed to fetch employees');
      const data = await response.json();
      setEmployees(data.results || []);
    } catch (error) {
      console.error('Error fetching employees:', error);
      toast({
        title: "Error",
        description: "Failed to fetch employees",
        variant: "destructive",
      });
    }
  };

  // Filter employees by department
  useEffect(() => {
    if (filters.department && filters.department !== 'all') {
      const filtered = employees.filter(emp => emp.department_id && emp.department_id.toString() === filters.department);
      setFilteredEmployees(filtered);
    } else {
      setFilteredEmployees(employees);
    }
  }, [filters.department, employees]);

  // Fetch performance results
  const fetchPerformanceResults = async () => {
    // Check if at least one filter is applied
    if (filters.category === 'all' && filters.department === 'all' && filters.team === 'all' && filters.employee_id === 'all') {
      toast({
        title: "Filter Required",
        description: "Please select at least one filter (Category, Department, Team, or Employee) to generate reports",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const params = new URLSearchParams();

      if (filters.category && filters.category !== 'all') {
        params.append('Category', filters.category);
      }

      if (filters.department && filters.department !== 'all') {
        params.append('Department', filters.department);
      }

      if (filters.team && filters.team !== 'all') {
        params.append('Team', filters.team);
      }

      if (filters.employee_id && filters.employee_id !== 'all') {
        params.append('employee_id', filters.employee_id);
      }

      const response = await fetch(`${BASE_URL}/performance-all-results/?${params.toString()}`, {
        headers: { 'Authorization': `Token ${token}` }
      });

      if (!response.ok) throw new Error('Failed to fetch performance results');
      const responseData = await response.json();
      setData(responseData.results || []);

      toast({
        title: "Success",
        description: `Found ${responseData.results?.length || 0} performance records`,
      });
    } catch (error) {
      console.error('Error fetching performance results:', error);
      toast({
        title: "Error",
        description: "Failed to fetch performance results",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Export to Excel
  const exportToExcel = async () => {
    if (data.length === 0) {
      console.log("Data:", data);
      toast({
        title: "No Data",
        description: "No data available to export",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    try {
      const workbook = XLSX.utils.book_new();
      const sheetData: any[][] = [];

      // Company Header Section (matching the template)
      sheetData.push(['OPTIVEN LIMITED']);
      sheetData.push(['Absa Tower 2nd floor, Loita St']);
      sheetData.push(['P.O BOX: 6525-00600 Nairobi']);
      sheetData.push(['E-Mail: <EMAIL>']);
      sheetData.push(['Mobile: 0790600600 / 0711601019']);
      sheetData.push([]); // Empty row

      // Report Title
      const reportTitle = filters.category && filters.category !== 'all'
        ? `${filters.category.toUpperCase()} PERFORMANCE REPORT`
        : 'PERFORMANCE REPORT';
      sheetData.push([reportTitle]);
      sheetData.push([`Generated on: ${new Date().toLocaleDateString()}`]);
      sheetData.push([]); // Empty row

      // Add headers based on category - different templates for Pillar vs Conversion
      let headers: string[];
      const hasConversionData = data.some(emp =>
        emp.what_results?.some(result =>
          result.MIB_target || result.Sales_target || result.MIB_Achieved || result.Sales_Achieved
        )
      );

      if (filters.category === 'Conversion' || (filters.category === 'all' && hasConversionData)) {
        headers = [
          'Team',
          'Team Leader',
          'Staff Name',
          'Designation',
          'KPI',
          'Monthly Target',
          'Quarterly Target',
          'Achieved',
          'Performance',
          'Staff Comments',
          'Team Leader Comments',
          'Other Comments'
        ];
      } else {
        // Default Pillar template
        headers = [
          'NO.',
          'Department',
          'Staff No.',
          'Staff Name',
          'Designation',
          'Team Leader',
          'KPI',
          'Email Address',
          'Staff Rating',
          'Staff Comments',
          'TL Rating',
          'TL Comments'
        ];
      }

      sheetData.push(headers);

      // Transform data to match template
      let rowNumber = 1;
      data.forEach((employee) => {
        // Skip employees with no performance data
        if (!employee.what_results || employee.what_results.length === 0) {
          return;
        }

        // Find employee email from employees list
        const employeeDetails = employees.find(emp => emp.employee_no_id === employee.employee_no);
        const emailAddress = employeeDetails?.email || 'N/A';

        employee.what_results.forEach((result) => {
          const isConversionData = result.MIB_target || result.Sales_target || result.MIB_Achieved || result.Sales_Achieved;

          // Get team leader - use Team_leader if available, otherwise use department_head
          const teamLeader = employee.Team_leader || employee.department_head || 'N/A';

          // Get KPI name from the 'what' object
          const kpiName = result.what?.what || 'N/A';

          if (filters.category === 'Conversion' || (filters.category === 'all' && isConversionData)) {
            // Conversion template format - handle different target types
            let monthlyTarget = 0;
            let achieved = 0;
            let targetType = '';

            // Determine which target/achieved to use based on KPI type
            if (kpiName === 'MIB TARGET' && result.MIB_target) {
              monthlyTarget = result.MIB_target;
              achieved = result.MIB_Achieved || 0;
              targetType = 'MIB';
            } else if (kpiName === 'SALES TARGET' && result.Sales_target) {
              monthlyTarget = result.Sales_target;
              achieved = result.Sales_Achieved || 0;
              targetType = 'Sales';
            } else {
              // For other KPIs without specific targets, show ratings instead
              monthlyTarget = 0;
              achieved = result.employee_rating || 0;
              targetType = 'Rating';
            }

            const quarterlyTarget = monthlyTarget > 0 ? (monthlyTarget * 3) : 0;
            const percentageAchieved = monthlyTarget > 0 ? ((achieved / monthlyTarget) * 100).toFixed(1) + '%' :
                                     (targetType === 'Rating' ? `${achieved} pts` : '0%');

            sheetData.push([
              employee.Team || 'N/A',
              teamLeader,
              employee.staff_name || 'N/A',
              employee.Designation || 'N/A',
              kpiName,
              monthlyTarget > 0 ? monthlyTarget.toLocaleString() : 'N/A',
              quarterlyTarget > 0 ? quarterlyTarget.toLocaleString() : 'N/A',
              targetType === 'Rating' ? `${achieved} pts` : achieved.toLocaleString(),
              percentageAchieved,
              result.employee_comments || '',
              result.supervisor_comments || '',
              result.manager_comments || result.extra_comments || ''
            ]);
          } else {
            // Pillar template format
            sheetData.push([
              rowNumber++,
              employee.department || 'N/A',
              employee.employee_no || 'N/A',
              employee.staff_name || 'N/A',
              employee.Designation || 'N/A',
              teamLeader,
              kpiName,
              emailAddress,
              result.employee_rating || '',
              result.employee_comments || '',
              result.supervisor_rating || '',
              result.supervisor_comments || ''
            ]);
          }
        });
      });

      // Add summary section
      sheetData.push([]); // Empty row
      sheetData.push(['SUMMARY']);
      sheetData.push(['Total Employees', data.length]);
      sheetData.push(['Total KPI Records', data.reduce((sum, emp) => sum + emp.what_results.length, 0)]);
      if (filters.category && filters.category !== 'all') {
        sheetData.push(['Report Category', filters.category]);
      }
      if (filters.department && filters.department !== 'all') {
        const deptName = departments.find(d => d.id.toString() === filters.department)?.name;
        sheetData.push(['Department Filter', deptName || 'Unknown']);
      }
      if (filters.team && filters.team !== 'all') {
        sheetData.push(['Team Filter', filters.team]);
      }
      if (filters.employee_id && filters.employee_id !== 'all') {
        const empName = employees.find(e => e.employee_no_id === filters.employee_id);
        sheetData.push(['Employee Filter', `${empName?.first_name} ${empName?.last_name} (${filters.employee_id})` || filters.employee_id]);
      }
      sheetData.push(['Generated By', 'HRMS System']);

      const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

      // Set column widths based on template
      if (filters.category === 'Conversion' || (filters.category === 'all' && hasConversionData)) {
        worksheet['!cols'] = [
          { wch: 15 },  // Team
          { wch: 20 },  // Team Leader
          { wch: 25 },  // Staff Name
          { wch: 25 },  // Designation
          { wch: 30 },  // KPI
          { wch: 15 },  // Monthly Target
          { wch: 15 },  // Quarterly Target
          { wch: 15 },  // Achieved
          { wch: 15 },  // Performance
          { wch: 50 },  // Staff Comments
          { wch: 50 },  // Team Leader Comments
          { wch: 50 }   // Other Comments
        ];
      } else {
        worksheet['!cols'] = [
          { wch: 5 },   // NO.
          { wch: 20 },  // Department
          { wch: 15 },  // Staff No.
          { wch: 25 },  // Staff Name
          { wch: 20 },  // Designation
          { wch: 20 },  // Team Leader
          { wch: 30 },  // KPI
          { wch: 25 },  // Email Address
          { wch: 12 },  // Staff Rating
          { wch: 50 },  // Staff Comments
          { wch: 12 },  // TL Rating
          { wch: 50 }   // TL Comments
        ];
      }

      // Apply styling to header rows to match the green template
      const headerRowIndex = 9; // 0-based index of the header row (after company header and title)
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

      // Style the header row with green background like the template
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: col });
        if (!worksheet[cellAddress]) continue;

        // Apply green background color matching the template
        worksheet[cellAddress].s = {
          font: { bold: true, color: { rgb: "000000" } },
          fill: { fgColor: { rgb: "92D050" } }, // Green color matching template
          alignment: { horizontal: "center", vertical: "center" },
          border: {
            top: { style: "thin", color: { rgb: "000000" } },
            bottom: { style: "thin", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } }
          }
        };
      }

      // Add borders to all data cells
      for (let row = headerRowIndex + 1; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!worksheet[cellAddress]) continue;

          worksheet[cellAddress].s = {
            border: {
              top: { style: "thin", color: { rgb: "000000" } },
              bottom: { style: "thin", color: { rgb: "000000" } },
              left: { style: "thin", color: { rgb: "000000" } },
              right: { style: "thin", color: { rgb: "000000" } }
            },
            alignment: { vertical: "top", wrapText: true }
          };
        }
      }

      const sheetName = filters.category && filters.category !== 'all' ? `${filters.category} Report` : 'Performance Report';
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

      const filePrefix = filters.category && filters.category !== 'all' ? filters.category : 'Performance';
      const fileName = `${filePrefix}_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast({
        title: "Export Successful",
        description: `Report exported as ${fileName}`,
      });
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export data to Excel",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Initialize data
  useEffect(() => {
    fetchDepartments();
    fetchTeams();
    fetchEmployees();
  }, []);

  return (
    <Screen>
      <div className="space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/performance-dashboard">Performance</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Performance All Results Reports</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Performance All Results Reports</h1>
            <p className="text-muted-foreground">
              Generate comprehensive performance reports for Pillars and Conversion categories
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={exportToExcel}
              disabled={isExporting || data.length === 0}
              className="flex items-center gap-2"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              Export to Excel
            </Button>
          </div>
        </div>

        {/* Filters Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Report Filters
            </CardTitle>
            <CardDescription>
              Select filters to generate performance reports. At least one filter is required. Team filter is available for Conversion category.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-6">
              {/* Category Filter */}
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={filters.category}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="Pillar">Pillar</SelectItem>
                    <SelectItem value="Conversion">Conversion</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Department Filter */}
              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Select
                  value={filters.department}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, department: value, employee_id: 'all' }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id.toString()}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Team Filter - Only show for Conversion category */}
              <div className="space-y-2">
                <Label htmlFor="team">Team</Label>
                <Select
                  value={filters.team}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, team: value, employee_id: 'all' }))}
                  disabled={filters.category !== 'Conversion' && filters.category !== 'all'}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All teams" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Teams</SelectItem>
                    {teams.map((team) => (
                      <SelectItem key={team.id} value={team.name}>
                        {team.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Employee Filter with integrated search */}
              <div className="space-y-2">
                <Label htmlFor="employee">Employee</Label>
                <Select
                  value={filters.employee_id}
                  onValueChange={(value) => {
                    setFilters(prev => ({ ...prev, employee_id: value }));
                    setEmployeeSearch(''); // Clear search when selection is made
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Search and select employee..." />
                  </SelectTrigger>
                  <SelectContent>
                    <div className="p-2 sticky top-0 bg-white z-10">
                      <Input
                        placeholder="Search by name or employee ID..."
                        value={employeeSearch}
                        onChange={(e) => {
                          e.stopPropagation();
                          setEmployeeSearch(e.target.value);
                        }}
                        onKeyDown={(e) => e.stopPropagation()}
                        className="mb-2"
                      />
                    </div>
                    <SelectItem value="all">All Employees</SelectItem>
                    {(filters.department && filters.department !== 'all' ? filteredEmployees : employees)
                      .filter(emp =>
                        !employeeSearch ||
                        `${emp.first_name} ${emp.last_name}`.toLowerCase().includes(employeeSearch.toLowerCase()) ||
                        emp.employee_no_id.toLowerCase().includes(employeeSearch.toLowerCase())
                      )
                      .slice(0, 50) // Limit to 50 results for performance
                      .map((emp) => (
                        <SelectItem key={emp.id} value={emp.employee_no_id}>
                          {emp.first_name} {emp.last_name} ({emp.employee_no_id})
                        </SelectItem>
                      ))}
                    {employeeSearch && (filters.department && filters.department !== 'all' ? filteredEmployees : employees)
                      .filter(emp =>
                        `${emp.first_name} ${emp.last_name}`.toLowerCase().includes(employeeSearch.toLowerCase()) ||
                        emp.employee_no_id.toLowerCase().includes(employeeSearch.toLowerCase())
                      ).length > 50 && (
                      <div className="p-2 text-sm text-muted-foreground text-center">
                        Showing first 50 results. Refine your search for more specific results.
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* Generate Button */}
              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button
                  onClick={fetchPerformanceResults}
                  disabled={loading}
                  className="w-full flex items-center gap-2"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                  Generate Report
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results Summary */}
        {loading ? (
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                <span className="ml-2 text-lg">Generating report...</span>
              </div>
            </CardContent>
          </Card>
        ) : data.length > 0 ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-green-600" />
                Report Generated Successfully
              </CardTitle>
              <CardDescription>
                Found {data.length} employees with performance data
                {filters.category && filters.category !== 'all' && ` for ${filters.category} category`}
                {filters.department && filters.department !== 'all' && ` in ${departments.find(d => d.id.toString() === filters.department)?.name} department`}
                {filters.team && filters.team !== 'all' && ` in ${filters.team} team`}
                {filters.employee_id && filters.employee_id !== 'all' && ` for ${employees.find(e => e.employee_no_id === filters.employee_id)?.first_name} ${employees.find(e => e.employee_no_id === filters.employee_id)?.last_name}`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="text-2xl font-bold text-green-700">{data.length}</div>
                  <p className="text-sm text-green-600">Employees Found</p>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="text-2xl font-bold text-blue-700">
                    {data.reduce((sum, emp) => sum + (emp.what_results?.length || 0), 0)}
                  </div>
                  <p className="text-sm text-blue-600">Total KPI Records</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                  <div className="text-2xl font-bold text-purple-700">
                    {filters.category && filters.category !== 'all' ? filters.category : 'All Categories'}
                  </div>
                  <p className="text-sm text-purple-600">Category</p>
                </div>
              </div>
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-yellow-600" />
                  <p className="text-sm text-yellow-800">
                    <strong>Ready for Export:</strong> Click the "Export to Excel" button above to download your formatted report with the company template.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (filters.category && filters.category !== 'all') || (filters.department && filters.department !== 'all') || (filters.team && filters.team !== 'all') || (filters.employee_id && filters.employee_id !== 'all') ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Results Found</h3>
              <p className="text-muted-foreground text-center">
                No performance data found for the selected filters. Try adjusting your search criteria.
              </p>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Target className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Select Filters to Begin</h3>
              <p className="text-muted-foreground text-center">
                Choose at least one filter (Category, Department, Team, or Employee) to generate performance reports.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </Screen>
  );
};

export default PerformanceAllResultsReports;
